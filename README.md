# Y3DHub

> **Note:** This repository is named **Y3DCore**, but the project itself is
> called **Y3DHub**.

## Deployment Architecture

Y3DHub uses a 3-directory deployment structure on the same server:

- **Development**: `/home/<USER>/Y3DHub_staging` (port 3002) - Current working directory
- **Staging**: `/home/<USER>/Y3DHub_staging_deploy` (port 3001) - Staging deployment for testing
- **Production**: `/home/<USER>/Y3DHub_production` (port 3000) - Production deployment

All three environments connect to the same MySQL database host (`*************`) with appropriate database names:

- Development & Staging: `y3dhub` database
- Production: `y3dhub_prod` database

## High-Level Architecture & System Overview

Here's a consolidated high-level overview of Y3DHub’s complete architecture, key dependencies, and core responsibilities:

### Repository and Project Layout

**Root Files:**

- Documentation (`README.md`, `docs/`)
- Configuration (`package.json`, `tsconfig.json`, `.env`)
- Database Schema (`prisma/schema.prisma`)
- 3D Models (`openscad/`)
- Deployment (`Dockerfile`, `docker-compose.litellm.yml`, `vercel.json`)
- Shell scripts (`scripts/`)

**src Directory:**

- `app`: Next.js App Router pages (login, orders, planner, print-queue, users), API routes (`/api/*`), middleware for authentication.
- `components`: Reusable React components and UI elements.
- `lib`: Core business logic (Prisma client setup, authentication, ShipStation integration, OpenSCAD CLI wrappers, AI prompt templates, email integration, marketplace utilities, data formatting, and logging).
- `db`: Prisma client and database wrappers.
- `scripts`: CLI automation scripts (sync-orders.ts, populate-print-queue.ts, complete-shipped-print-tasks.ts, various utility scripts).
- `workers`: Background tasks (stl-render-worker.ts).
- `types`, `styles`, `public`, `utils`, `tests`: Supporting assets and unit tests.

### Key Dependencies

- **Framework & UI:** Next.js 14, React 18, Tailwind CSS, NextUI, Radix UI, Framer Motion, Tanstack React Table, Chart.js, Recharts.
- **Data & ORM:** Prisma 6 with MySQL backend.
- **Authentication & Email:** NextAuth.js, SendGrid.
- **Marketplaces & APIs:** ShipStation SDK, OpenAI.
- **3D Rendering:** OpenSCAD CLI, bwip-js for barcode generation.
- **Utilities & Tooling:** dotenv, yargs, commander, lodash.debounce, pino/winston logging, zod for validation, Husky, lint-staged, ESLint, Prettier, Vitest.

### Core Workflows & Functionality

#### A. Order Synchronisation

- Automated syncing from ShipStation (`sync-orders.ts`) to database (`Order`, `Customer`, `OrderItem`).
- Performance and error tracking (`ScriptRunLog`, `SyncMetrics`).

#### B. Print-Task Generation & AI Extraction

- Scanning new orders, breaking down OrderItems, invoking AI to extract personalisation details.
- Mapping products using SKU for correct OpenSCAD model and parameters, populating PrintOrderTask records.
- AI call logs stored for monitoring and manual review flags for uncertain tasks.

#### C. STL Rendering Worker

- Polls pending PrintOrderTask entries.
- Renders STL files via OpenSCAD, stores in `public/stl`, optionally uploads to Google Drive.
- Handles retries, updates status and error logging.

#### D. Task Status Management

- Automatic status updates as orders ship (`complete-shipped-print-tasks.ts`).
- API and CLI scripts for status management.

#### E. AI Reports & Print Optimisation

- AI-assisted optimisation of print tasks (minimising colour changes, efficient plate packing).
- Report generation scheduled or ad-hoc, integrated into dashboards.

#### F. Web UI & API

- Front-end built with Next.js App Router, protected via NextAuth middleware.
- Interactive dashboards, detailed modals, charts, API endpoints for CRUD operations on orders, tasks, users, and webhook integrations.

#### G. Authentication & Authorisation

- Managed by NextAuth.js with Prisma adapter, protected routes (`middleware.ts`).
- User roles and management through database and custom logic.

#### H. Monitoring & Logging

- System and script run logs via Pino/Winston.
- Application metrics available for external monitoring (Grafana, Prometheus).

### Summary

Y3DHub represents a robust, full-stack Next.js and Prisma-based platform that handles automated order syncing, AI-driven personalisation and print-task management, dynamic STL generation, and comprehensive web-based management and analytics capabilities. Major next steps involve enabling full AI integrations, further test coverage, and cleaning up legacy code.

Y3DHub is a modern Next.js application for managing 3D printing tasks and orders, featuring integrations with ShipStation API, OpenAI-powered text extraction, and comprehensive task management for 3D printing businesses.

## Features

- **Order Management**: Synchronize and manage orders from ShipStation
- **Print Task Workflow**: Track tasks through pending → in progress → completed states
- **STL Rendering**: Automated STL file generation for personalized products
- **AI-powered Text Extraction**: Extract personalization text using OpenAI
- **Product Mapping**: Normalize product names across marketplaces
- **Multi-marketplace Support**: Amazon, Etsy, eBay integrations
- **Task Review System**: Review and approve uncertain extractions

## Technology Stack

- **Frontend**: Next.js 14 (App Router), React 18, NextUI, TailwindCSS
- **Backend**: Next.js API Routes
- **Database**: MySQL with Prisma ORM
- **Integrations**: ShipStation API, OpenAI API
- **Rendering**: OpenSCAD for 3D model generation
- **Authentication**: NextAuth.js

## ⚠️ CRITICAL: Development Workflow

**🚨 NEVER WORK DIRECTLY ON THE MAIN BRANCH 🚨**

### Mandatory Branch Workflow

1. **ALWAYS** work on the `staging` branch for all development
2. **ALWAYS** commit changes to `staging` first
3. **ALWAYS** push to `staging` to trigger staging deployment (port 3001)
4. **ONLY** merge `staging` → `main` for production deployment (port 3000)

```bash
# ✅ CORRECT WORKFLOW
git checkout staging           # Work on staging
# ... make changes ...
git add . && git commit -m "..."
git push origin staging        # Triggers staging deployment

# After testing staging:
git checkout main
git merge staging              # Merge tested changes
git push origin main           # Triggers production deployment
```

**⚡ This workflow is MANDATORY for all Y3DHub development ⚡**

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- MySQL Database
- OpenSCAD (for STL rendering)
- ShipStation API credentials
- OpenAI API key

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/y3dltd/Y3DCore.git
   cd y3dhub
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Set up environment variables

   ```bash
   cp .env.example .env
   # Edit .env with your database, ShipStation, and OpenAI credentials
   ```

4. Generate Prisma client

   ```bash
   npx prisma generate
   ```

5. Run database migrations

   ```bash
   npx prisma migrate dev
   ```

6. Start the development server

   ```bash
   # Development server (port 3002)
   npm run dev

   # Alternative: Production-like server (port 3000)
   npm run start-prod-http-3000
   ```

### Multi-Environment Setup

The development environment is isolated from deployment environments:

- **Development**: Uses local `.env` file, runs on port 3002
- **Staging/Production**: Use static `.env` files in their deployment directories
- **Database**: Development and staging share `y3dhub`, production uses `y3dhub_prod`

7. Linting and code quality

   ```bash
   # Run standard linting (✅ Currently 0 errors, 0 warnings)
   npm run lint

   # Fix linting issues automatically where possible
   npm run lint:fix

   # Run comprehensive linting on src directory
   npm run lint:full

   # Run linting specifically for CI environments
   npm run lint:ci
   ```

   **✅ Code Quality Status**: The codebase is currently **100% clean** with:

   - ✅ 0 ESLint errors
   - ✅ 0 ESLint warnings
   - ✅ Full TypeScript 5.8.3 compatibility
   - ✅ Optimized VS Code configuration
   - ✅ Prettier formatting setup

   For more details about the linting process, see [docs/linting.md](docs/linting.md).

## Project Structure

```
y3dhub/
├── src/                       # Main source code
│   ├── app/                   # Next.js App Router pages and API routes
│   │   ├── api/               # API routes for orders, tasks, etc.
│   │   ├── orders/            # Order management pages
│   │   └── print-queue/       # Print task management pages
│   ├── components/            # React components
│   ├── lib/                   # Shared libraries
│   │   ├── db/                # Database utilities (Prisma)
│   │   ├── shipstation/       # ShipStation integration
│   │   ├── ai/                # AI/OpenAI integration
│   │   └── openscad/          # OpenSCAD integration for STL rendering
│   ├── scripts/               # Backend scripts for task management
│   │   ├── sync-orders.ts     # Order synchronization from ShipStation
│   │   ├── populate-print-queue.ts # Print task generation
│   │   └── complete-shipped-print-tasks.ts # Update shipped order tasks
│   ├── types/                 # TypeScript type definitions
│   └── workers/               # Background workers
│       └── stl-render-worker.ts # STL generation worker
├── prisma/                    # Prisma schema and migrations
├── scripts/                   # System automation scripts
│   └── workflow.sh            # Main workflow automation script
├── tools/                     # Developer wrapper scripts
│   ├── aider-wrapper.sh       # Generic Aider helper
│   ├── bedrock-wrapper.sh     # AWS Bedrock chat helper
│   ├── claude-wrapper.sh      # Anthropic Claude chat helper
│   ├── codex-wrapper.sh       # Quickly open GPT chat with code context
│   ├── codex-cli-wrapper.sh   # Launch OpenAI Codex CLI in repo context
│   ├── fullcontext-wrapper.sh # Aider helper for huge context models
│   ├── gemini-wrapper.sh      # Google Gemini chat helper
│   └── vertex-wrapper.sh      # Vertex AI chat helper
├── openscad/                  # OpenSCAD models and scripts
├── docs/                      # Documentation
└── public/                    # Static assets and rendered STL files
```

## Core Workflows

### 1. Order Synchronization

The system synchronizes orders from ShipStation and creates print tasks:

```bash
# Sync recent orders from ShipStation
npx tsx src/scripts/sync-orders.ts --recent

# Generate print tasks from synced orders
npx tsx src/scripts/populate-print-queue.ts
```

### 2. Print Queue Dashboard

Print tasks generated by `populate-print-queue.ts` can be reviewed and managed
via the `/print-queue` route in the web interface. A zero-byte file named
`print-queue` previously lived at the repository root as a placeholder and has
been removed.

### 3. STL Rendering Worker

The STL rendering worker processes pending tasks and generates 3D models:

```bash
# Start the STL rendering worker
npm run worker:stl

# Reset stuck or failed tasks back to pending state
npm run worker:stlrefresh

# Process a specific task by ID
npx tsx src/workers/stl-render-worker.ts --task=task_id

# Clear all files from Google Drive (when GDRIVE_ENABLED=true)
npm run worker:stl-clear-gdrive
```

The worker supports Google Drive integration for automatic file uploads:

- Set `GDRIVE_ENABLED=true` in your `.env` file to enable uploads
- Configure `GDRIVE_FOLDER_ID` with your target Google Drive folder ID
- Set `GDRIVE_SERVICE_ACCOUNT_PATH` to the path of your service account JSON key file
- Files are organized in folders by product type and first letter of customization
- Special handling is implemented for different product SKUs:
  - `PER-KEY3D*`: Uses DualColour.scad with simple text parameters
  - `REG-KEY*`: Uses registration_keys/reg_key_v2.scad with registration numbers
  - `PER-2PER-*` (cable clips): Generates both 3.5mm and 4.0mm versions

### 4. Task Status Management

Complete tasks for shipped orders:

```bash
# Mark tasks as completed when orders are shipped
npx tsx src/scripts/complete-shipped-print-tasks.ts
```

### 5. Automated Workflow

The entire process can be automated using the workflow script:

```bash
# Run the complete workflow
./scripts/workflow.sh
```

## Deployment

Y3DHub uses GitHub Actions for automated deployment to multiple environments with optimized workflows.

### Environment Structure

- **Development** (`/home/<USER>/Y3DHub_staging`): Port 3002 - Local development
- **Staging** (`/home/<USER>/Y3DHub_staging_deploy`): Port 3001 - Testing staging features
- **Production** (`/home/<USER>/Y3DHub_production`): Port 3000 - Live production system

### Automated Workflows

- **Push to `staging`**: Triggers 1 workflow (staging deployment only)
- **Push to `main`**: Triggers 2 workflows (production deployment + prebuild)
- **Pull Requests**: All quality checks (CI, linting, type checking)

### Deployment Features

✅ **Optimized workflow triggers** (reduced from 4 to 1-2 workflows per push)
✅ **Static environment configuration** (no dynamic file copying)
✅ **Zero-downtime deployments** with PM2 process management
✅ **Automatic health checks** after deployment
✅ **Environment isolation** with separate databases and ports

```bash
# Manual deployment (if needed)
# Workflows support manual triggering via GitHub Actions UI

# Check deployment status
curl http://localhost:3000/  # Production
curl http://localhost:3001/  # Staging
curl http://localhost:3002/  # Development
```

For detailed workflow information, see [`docs/WORKFLOWS.md`](docs/WORKFLOWS.md).

## Documentation

Detailed documentation is available in the `/docs` directory:

- Development guides
- API reference
- Database schema
- Integration details
- Troubleshooting tips

## Authentication (Implemented with NextAuth.js)

Authentication is handled using **Auth.js (NextAuth.js)** with the following setup:

- **Strategy:** JWT (JSON Web Tokens) sessions.
- **Provider:** Credentials (email/password).
- **Adapter:** `@auth/prisma-adapter` storing user, account, session, and verification token data in the database.
- **Configuration:** Core options are defined in `src/lib/auth.ts` and used by the route handler `src/app/api/auth/[...nextauth]/route.ts`.
- **Protection:**
  - **Pages:** Middleware (`src/middleware.ts`) protects most pages (excluding `/login`, `/api`, static assets) by redirecting unauthenticated users to `/login`.
  - **API Routes:** Sensitive API routes (e.g., task updates, sync) use `getServerSession` from `next-auth/next` to verify the user's session server-side.
- **Frontend:**
  - The root layout (`src/app/layout.tsx`) is wrapped in `<SessionProvider>` via `src/app/SessionProviderWrapper.tsx`.
  - The login page (`src/app/login/page.tsx`) uses `signIn()` from `next-auth/react`.
  - The Navbar (`src/components/layout/navbar.tsx`) uses `useSession()` to display user status and email.
  - The Logout Button (`src/components/layout/logout-button.tsx`) uses `signOut()` from `next-auth/react`.
- **Required Environment Variables:**
  - `NEXTAUTH_SECRET`: A strong secret key for signing tokens.
  - `NEXTAUTH_URL`: The canonical URL of the deployment (handled automatically by Vercel for `.vercel.app` URLs, needs explicit setting for custom domains or local dev).
  - `DATABASE_URL`: Required by the Prisma adapter.

**Note on Migrations:** Due to previous inconsistencies, the Prisma migration history needs to be resolved (likely via `prisma migrate reset` or manual intervention) before `prisma migrate dev` can be used for future schema changes.

---

## Planner v2 (fast mode)

An alternative backend that produces print plans quickly. Enable with `USE_PLANNER_V2=true`.

Environment variables:

- `PLANNER_V2_CONCURRENCY` – number of parallel AI calls (default 5)
- `PLANNER_V2_MODEL` – override OpenAI model (default gpt-3.5-turbo-1106)

## Email (SendGrid)

The application uses SendGrid for sending emails.

- **Configuration:** The `SENDGRID_API_KEY` environment variable must be set. The default sender address is configured via `SENDGRID_FROM_EMAIL`.
- **Utility:** The core email sending logic is in `src/lib/email/send-email.ts`. It provides an `initSendGrid()` function (called automatically by `sendEmail`) and a `sendEmail({ to, from?, subject, text?, html? })` helper function.
- **Testing:** A test script is available to verify the integration:
  - Set `SENDGRID_TEST_TO` (or `SENDGRID_TO_EMAIL`) in your environment to the desired recipient address.
  - Run `npm run email:test`.
  - This will send a basic text email using the configured API key and addresses.

---

### SSH

ssh -i C:/Users/<USER>/.ssh/id_rsa jayson_yorkshire3d_co_uk@**************

## License

[Proprietary] © 2025 Yorkshire3D Limited. All rights reserved.
