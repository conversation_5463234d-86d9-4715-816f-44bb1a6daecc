'use client';

import { Arrow<PERSON>athIcon, CheckIcon, PlayIcon } from '@heroicons/react/24/outline';
import { <PERSON>ert, Button, Progress, Select, SelectItem, Spinner, Tooltip } from '@nextui-org/react';
import { PrintOrderTask_status } from '@prisma/client';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { toast } from '@/lib/ui/toast';
import { PrintTaskCardProps } from '@/types/print-tasks';

import TaskCarousel from './TaskCarousel';
import { TaskTimeline } from './TaskTimeline';

interface TaskPageProps {
  tasks: PrintTaskCardProps[];
  stats: {
    totalTasks: number;
    totalItems: number;
    pendingTasks: number;
    completedTasks: number;
    lastUpdated: string;
  };
  isLoading: boolean;
  isOptimizing: boolean;
  optimizingElapsedTime: number;
  error: string | null;
  onRefresh: () => void;
  onGeneratePlan: (_promptVersion?: string) => void;
  onGenerateTodayPlan: (_promptVersion?: string) => void;
  onGenerateTodayTomorrowPlan: (_promptVersion?: string) => void;
  setTasks: React.Dispatch<React.SetStateAction<PrintTaskCardProps[]>>;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  recentRuns?: { id: string; finishedAt: string }[];
  selectedRunId?: string | null;
  onSelectRun?: (
    // Parameter intentionally unused
    _id: string | null
  ) => void;
  promptVersion?: string;
  onPromptVersionChange?: (_version: string) => void;
}

const TaskPage: React.FC<TaskPageProps> = ({
  tasks,
  stats,
  isLoading,
  isOptimizing,
  optimizingElapsedTime,
  error,
  onRefresh,
  onGeneratePlan,
  onGenerateTodayPlan,
  onGenerateTodayTomorrowPlan,
  setTasks,
  setError,
  recentRuns = [],
  selectedRunId = null,
  onSelectRun,
  promptVersion = 'v8',
  onPromptVersionChange,
}) => {
  const [activeTaskId, setActiveTaskId] = useState<string | null>(
    tasks.length > 0 ? tasks[0].taskId : null
  );

  const carouselRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  // Handle scrolling to task when timeline item is clicked
  const handleTaskSelect = useCallback((taskId: string) => {
    setActiveTaskId(taskId);
    const element = document.getElementById(`task-${taskId}`);
    if (element && carouselRef.current) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Handler for individual item status change (passed to Carousel)
  const handleItemStatusChange = useCallback(
    async (taskId: string, itemId: string, newStatus: PrintOrderTask_status) => {
      // Find the task and item index
      const taskIndex = tasks.findIndex(t => t.taskId === taskId);
      if (taskIndex === -1) return;

      const task = tasks[taskIndex];
      if (!task) return;

      const itemIndex = task.items.findIndex(item => item.name === itemId);
      if (itemIndex === -1) return;

      // Store previous state for potential rollback
      const previousTasks = JSON.parse(JSON.stringify(tasks));

      // Optimistic Update
      const updatedTasks = tasks.map((t, idx) => {
        if (idx === taskIndex) {
          const updatedItems = t.items.map((item, iIdx) => {
            if (iIdx === itemIndex) {
              return { ...item, status: newStatus };
            }
            return item;
          });
          // Recalculate task status based on items (optional, might depend on logic)
          // const allComplete = updatedItems.every(i => i.status === PrintOrderTask_status.completed);
          // const taskStatus = allComplete ? PrintOrderTask_status.completed : PrintOrderTask_status.pending;
          return { ...t, items: updatedItems /*, status: taskStatus */ };
        }
        return t;
      });
      setTasks(updatedTasks); // Use prop setter

      // API Call to update the specific item status
      try {
        // Use the actual item ID (item.name) for the API call
        const response = await fetch(`/api/print-tasks/${itemId}/status`, {
          // Use itemId
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status: newStatus }),
        });
        if (!response.ok) {
          const err = await response.json().catch(() => ({ error: 'Failed to update item' }));
          throw new Error(err.error || 'Failed to update item');
        }
        toast.success('Item status updated');
      } catch (error) {
        console.error('Failed to update task status via API:', error);
        // Revert optimistic update on API error
        setTasks(previousTasks); // Use prop setter
        setError(`Failed to update status for task ${taskId}: ${(error as Error).message}`); // Use prop setter
      }
    },
    [tasks, setTasks, setError] // Use prop setters in dependency array
  );

  // Handler for bulk task status change (passed to Carousel)
  const handleBulkStatusChange = useCallback(
    async (taskId: string, newStatus: PrintOrderTask_status) => {
      const taskIndex = tasks.findIndex(t => t.taskId === taskId);
      const task = tasks[taskIndex];
      if (!task) return;

      const previousTasks = JSON.parse(JSON.stringify(tasks));

      // Optimistic update – update the UI immediately
      const updatedTasks = tasks.map((t, idx) => {
        if (idx === taskIndex) {
          return {
            ...t,
            items: t.items.map(item => ({ ...item, status: newStatus })),
          };
        }
        return t;
      });
      setTasks(updatedTasks);

      // Use raw item IDs (CUID strings) directly for the API call
      const itemIds = task.items.map(item => item.name).filter(Boolean);

      // If somehow no IDs are found, abort API call to avoid validation errors
      if (itemIds.length === 0) {
        console.warn('[TaskPage] handleBulkStatusChange: No valid item IDs found for task', taskId);
        return;
      }

      // API Call for bulk update
      try {
        const response = await fetch(`/api/print-tasks/bulk-status`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ taskIds: itemIds, status: newStatus }), // Send CUID string IDs
        });
        if (!response.ok) {
          const err = await response.json().catch(() => ({ error: 'Bulk update failed' }));
          throw new Error(err.error || 'Bulk update failed');
        }
        toast.success('Items updated');
      } catch (error) {
        console.error('Failed to bulk update task status via API:', error);
        // Revert optimistic update on API error
        setTasks(previousTasks); // Use prop setter
        setError(`Failed to bulk update status for task ${taskId}: ${(error as Error).message}`); // Use prop setter
      }
    },
    [tasks, setTasks, setError] // Use prop setters in dependency array
  );

  // --- Global Bulk Status Handler (Set All In Progress / Set All Complete) ---
  const handleGlobalStatusChange = useCallback(
    async (newStatus: PrintOrderTask_status) => {
      if (tasks.length === 0) return;

      const previousTasks = JSON.parse(JSON.stringify(tasks));

      // Optimistic update – set every item's status
      const updatedTasks = tasks.map(task => ({
        ...task,
        items: task.items.map(item => ({ ...item, status: newStatus })),
      }));
      setTasks(updatedTasks);

      // Collect all unique, valid item IDs
      const allItemIds = tasks.flatMap(task => task.items.map(item => item.name)).filter(Boolean);

      if (allItemIds.length === 0) {
        console.warn('[TaskPage] handleGlobalStatusChange: no valid item IDs found');
        return;
      }

      try {
        await fetch(`/api/print-tasks/bulk-status`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ taskIds: allItemIds, status: newStatus }),
        });
      } catch (error) {
        console.error('Failed to globally update task statuses via API:', error);
        setTasks(previousTasks);
        setError(`Failed to update all tasks: ${(error as Error).message}`);
      }
    },
    [tasks, setTasks, setError]
  );

  // --- Derived Global Progress Metrics ---
  const totalItemsGlobal = tasks.reduce(
    (sum, task) => sum + task.items.reduce((q, item) => q + (item.quantity ?? 1), 0),
    0
  );
  const completedItemsGlobal = tasks.reduce(
    (sum, task) =>
      sum +
      task.items
        .filter(item => item.status === PrintOrderTask_status.completed)
        .reduce((q, item) => q + (item.quantity ?? 1), 0),
    0
  );
  const anyItemInProgressGlobal = tasks.some(task =>
    task.items.some(item => item.status === PrintOrderTask_status.in_progress)
  );
  const allItemsCompletedGlobal = totalItemsGlobal > 0 && completedItemsGlobal === totalItemsGlobal;

  // --- Dynamic Task Completion Stats ---
  const derivedTotalTasks = tasks.length;
  const derivedCompletedTasks = useMemo(
    () =>
      tasks.filter(task =>
        task.items.every(item => item.status === PrintOrderTask_status.completed)
      ).length,
    [tasks]
  );
  // Count tasks with any items currently in progress
  const derivedInProgressTasks = useMemo(
    () =>
      tasks.filter(task =>
        task.items.some(item => item.status === PrintOrderTask_status.in_progress)
      ).length,
    [tasks]
  );

  // Set up scroll listener to update active task
  useEffect(() => {
    const handleScroll = () => {
      if (!carouselRef.current) return;

      const elements = Array.from(carouselRef.current.querySelectorAll('section[id^="task-"]'));
      const headerHeight = headerRef.current?.clientHeight || 0;

      // Find the first element whose top is within viewport
      for (const element of elements) {
        const rect = element.getBoundingClientRect();
        if (rect.top <= headerHeight + 100 && rect.bottom > headerHeight) {
          const taskId = element.id.replace('task-', '');
          if (taskId !== activeTaskId) {
            setActiveTaskId(taskId);
          }
          break;
        }
      }
    };

    const carouselElement = carouselRef.current;
    if (carouselElement) {
      carouselElement.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (carouselElement) {
        carouselElement.removeEventListener('scroll', handleScroll);
      }
    };
  }, [activeTaskId]);

  // --- Handler for selecting a historical run ---
  const handleRunSelect = useCallback(
    (keys: unknown) => {
      if (!onSelectRun) return;
      // NextUI passes a Set of keys (or "all"), we only allow single selection here
      const keyArray = Array.isArray(keys)
        ? (keys as unknown as React.Key[])
        : Array.from(keys as Set<React.Key>);
      const firstKey = keyArray[0] as string | undefined;
      if (firstKey === undefined) return;
      // "latest" denotes the most recent successful run (live data)
      onSelectRun(firstKey === 'latest' ? null : firstKey);
    },
    [onSelectRun]
  );
  return (
    <div className="flex flex-col h-full">
      {/* Header/Summary Card */}
      <div
        ref={headerRef}
        className="sticky top-0 z-10 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl mb-6"
      >
        <div className="flex justify-between items-start mb-3">
          <div>
            <h1 className="text-3xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-500 mb-1">
              Print Tasks
            </h1>
            <p className="text-base text-gray-600 dark:text-gray-400 mb-1">
              {derivedTotalTasks} Tasks
              <span className="mx-1">·</span>
              {stats.totalItems} Items
            </p>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            {onPromptVersionChange && (
              <Select
                size="sm"
                selectedKeys={new Set([promptVersion])}
                onSelectionChange={keys => {
                  const selectedKey = Array.from(keys)[0] as string;
                  onPromptVersionChange(selectedKey);
                }}
                aria-label="Select prompt version"
                placeholder="Prompt Version"
                className="min-w-[120px]"
                items={[
                  { id: 'v1', label: 'Prompt V1' },
                  { id: 'v2', label: 'Prompt V2' },
                  { id: 'v3', label: 'Prompt V3' },
                  { id: 'v4', label: 'Prompt V4' },
                  { id: 'v6', label: 'Prompt V6' },
                  { id: 'v7', label: 'Prompt V7' },
                  { id: 'v8', label: 'Prompt V8' },
                ]}
              >
                {item => (
                  <SelectItem key={item.id} textValue={item.label}>
                    {item.label}
                  </SelectItem>
                )}
              </Select>
            )}
            {recentRuns.length > 0 && (
              <Select
                size="sm"
                selectedKeys={new Set([selectedRunId ?? 'latest'])}
                onSelectionChange={handleRunSelect}
                aria-label="Select previous optimisation run"
                placeholder="Run history"
                className="min-w-[160px]"
                items={[
                  { id: 'latest', label: 'Latest Run' },
                  ...recentRuns.map(run => ({
                    id: run.id,
                    label: new Date(run.finishedAt).toLocaleString(),
                  })),
                ]}
              >
                {item => (
                  <SelectItem key={item.id} textValue={item.label}>
                    {item.label}
                  </SelectItem>
                )}
              </Select>
            )}
            <Tooltip content="Refresh Plan">
              <Button
                isIconOnly
                variant="light"
                onPress={onRefresh}
                disabled={isLoading || isOptimizing}
              >
                <ArrowPathIcon className="h-5 w-5" />
              </Button>
            </Tooltip>
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-end gap-4 mb-4">
          <div className="flex flex-wrap gap-2 items-center">
            <Tooltip content="Generate Plan for Today's Orders Only">
              <Button
                type="button"
                onPress={() => onGenerateTodayPlan(promptVersion)}
                isLoading={isOptimizing}
                disabled={isLoading || isOptimizing}
                size="md"
                className="bg-slate-700 hover:bg-slate-600 text-white dark:bg-slate-600 dark:hover:bg-slate-500"
              >
                Today
              </Button>
            </Tooltip>
            <Tooltip content="Generate Plan for Today & Tomorrow Orders Only">
              <Button
                type="button"
                color="warning"
                variant="solid"
                onPress={() => onGenerateTodayTomorrowPlan(promptVersion)}
                isLoading={isOptimizing}
                disabled={isLoading || isOptimizing}
                size="md"
              >
                Today &amp; Tomorrow
              </Button>
            </Tooltip>
            <Tooltip content="Generate New Optimized Plan for All Pending Orders">
              <Button
                type="button"
                onPress={() => onGeneratePlan(promptVersion)}
                isLoading={isOptimizing}
                disabled={isLoading || isOptimizing}
                startContent={!isOptimizing && <PlayIcon className="h-5 w-5" />}
                size="md"
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200"
              >
                {isOptimizing ? `Optimizing... (${optimizingElapsedTime}s)` : 'Generate Plan'}
              </Button>
            </Tooltip>
          </div>

          <div className="text-sm flex flex-col sm:flex-row sm:items-start gap-x-6 gap-y-2 mt-2 md:mt-0 flex-grow md:justify-end">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {derivedCompletedTasks} of {derivedTotalTasks} tasks completed
              </span>
              <span className="px-2 py-0.5 bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100 text-xs font-semibold rounded-full">
                {derivedTotalTasks > 0
                  ? Math.round((derivedCompletedTasks / derivedTotalTasks) * 100)
                  : 0}
                %
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {derivedInProgressTasks} of {derivedTotalTasks} tasks in progress
              </span>
              <span className="px-2 py-0.5 bg-yellow-100 text-yellow-700 dark:bg-yellow-700 dark:text-yellow-100 text-xs font-semibold rounded-full">
                {derivedTotalTasks > 0
                  ? Math.round((derivedInProgressTasks / derivedTotalTasks) * 100)
                  : 0}
                %
              </span>
            </div>
          </div>
        </div>

        {/* Global progress & bulk buttons */}
        <div className="flex flex-col gap-2">
          <div className="flex flex-col gap-1">
            <Progress
              aria-label="Overall progress"
              value={totalItemsGlobal === 0 ? 0 : (completedItemsGlobal / totalItemsGlobal) * 100}
              size="sm"
              color={
                allItemsCompletedGlobal
                  ? 'success'
                  : anyItemInProgressGlobal
                    ? 'primary'
                    : 'warning'
              }
              className="w-full"
            />
            <span className="text-xs text-gray-500 dark:text-gray-400 self-end">
              {completedItemsGlobal} of {totalItemsGlobal} items completed
            </span>
          </div>
          <div className="flex gap-2 self-end">
            <Button
              color="warning"
              variant="flat"
              size="sm"
              startContent={<PlayIcon className="h-4 w-4" />}
              onPress={() => handleGlobalStatusChange(PrintOrderTask_status.in_progress)}
              isDisabled={allItemsCompletedGlobal || anyItemInProgressGlobal}
            >
              Set All In Progress
            </Button>
            <Button
              color="success"
              variant="flat"
              size="sm"
              startContent={<CheckIcon className="h-4 w-4" />}
              onPress={() => handleGlobalStatusChange(PrintOrderTask_status.completed)}
              isDisabled={allItemsCompletedGlobal}
            >
              Set All Complete
            </Button>
          </div>
        </div>

        {error && (
          <Alert color="danger" className="mt-4">
            {error}
          </Alert>
        )}
        {isLoading && !isOptimizing && (
          <div className="flex justify-center items-center p-4 mt-4">
            <Spinner label="Loading Plan..." color="primary" />
          </div>
        )}
      </div>

      {/* Container to center the Timeline and Carousel block horizontally */}
      <div className="flex justify-center overflow-hidden h-full">
        <TaskTimeline tasks={tasks} onTaskSelect={handleTaskSelect} activeTaskId={activeTaskId} />
        <main ref={carouselRef} className="flex flex-col p-4 md:p-6 overflow-y-auto h-full">
          <div className="w-full max-w-screen-xl">
            <TaskCarousel
              tasks={tasks}
              onTaskStatusChange={handleItemStatusChange}
              onBulkTaskStatusChange={handleBulkStatusChange}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default TaskPage;
