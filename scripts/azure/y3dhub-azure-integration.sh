#!/bin/bash

# Y3DHub Azure File Share Integration Script
# This script provides utilities for backing up and sharing Y3DHub data via Azure

set -e

AZURE_MOUNT="/mnt/azure-fileshare"
BACKUP_DIR="$AZURE_MOUNT/backups"
LOGS_DIR="$AZURE_MOUNT/logs"
UPLOADS_DIR="$AZURE_MOUNT/uploads"
SHARED_DIR="$AZURE_MOUNT/shared"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

# Check if Azure mount is available
check_azure_mount() {
    if ! mountpoint -q "$AZURE_MOUNT"; then
        echo_error "Azure File Share not mounted at $AZURE_MOUNT"
        echo_info "Run: sudo mount -a"
        exit 1
    fi
    echo_success "Azure File Share is mounted and accessible"
}

# Backup MySQL database
backup_database() {
    local db_name="${1:-y3dhub_prod}"
    local backup_file="$BACKUP_DIR/mysql-${db_name}-$(date +%Y%m%d_%H%M%S).sql"
    
    echo_info "Backing up database: $db_name"
    
    if mysqldump -u root -pDmggg130319188*** "$db_name" > "$backup_file"; then
        echo_success "Database backup saved: $backup_file"
        
        # Compress the backup
        gzip "$backup_file"
        echo_success "Backup compressed: ${backup_file}.gz"
        
        # Keep only last 7 days of backups
        find "$BACKUP_DIR" -name "mysql-${db_name}-*.sql.gz" -mtime +7 -delete
        echo_info "Cleaned up old backups (>7 days)"
    else
        echo_error "Database backup failed"
        return 1
    fi
}

# Backup application files
backup_application() {
    local env="${1:-production}"
    local source_dir
    
    case $env in
        "production")
            source_dir="/home/<USER>/Y3DHub_production"
            ;;
        "staging")
            source_dir="/home/<USER>/Y3DHub_staging"
            ;;
        *)
            echo_error "Unknown environment: $env"
            return 1
            ;;
    esac
    
    local backup_file="$BACKUP_DIR/y3dhub-${env}-$(date +%Y%m%d_%H%M%S).tar.gz"
    
    echo_info "Backing up $env application files from $source_dir"
    
    if tar -czf "$backup_file" -C "$(dirname "$source_dir")" "$(basename "$source_dir")" \
        --exclude="node_modules" \
        --exclude=".git" \
        --exclude=".next" \
        --exclude="logs" \
        --exclude="output_stl"; then
        
        echo_success "Application backup saved: $backup_file"
        
        # Keep only last 3 days of app backups (they're larger)
        find "$BACKUP_DIR" -name "y3dhub-${env}-*.tar.gz" -mtime +3 -delete
        echo_info "Cleaned up old application backups (>3 days)"
    else
        echo_error "Application backup failed"
        return 1
    fi
}

# Archive logs to Azure
archive_logs() {
    local log_archive="$LOGS_DIR/y3dhub-logs-$(date +%Y%m%d_%H%M%S).tar.gz"
    
    echo_info "Archiving Y3DHub logs"
    
    if tar -czf "$log_archive" -C /var/log y3dhub/ 2>/dev/null; then
        echo_success "Logs archived: $log_archive"
        
        # Keep only last 30 days of log archives
        find "$LOGS_DIR" -name "y3dhub-logs-*.tar.gz" -mtime +30 -delete
        echo_info "Cleaned up old log archives (>30 days)"
    else
        echo_warning "No logs found to archive or archive failed"
    fi
}

# Share STL files
share_stl_files() {
    local stl_source="/home/<USER>/Y3DHub_production/output_stl"
    local stl_dest="$SHARED_DIR/stl-files"
    
    echo_info "Sharing STL files to Azure"
    
    mkdir -p "$stl_dest"
    
    if [ -d "$stl_source" ] && [ "$(ls -A "$stl_source")" ]; then
        cp "$stl_source"/*.stl "$stl_dest"/ 2>/dev/null || true
        echo_success "STL files copied to shared directory"
        
        # List what was copied
        echo_info "Available STL files:"
        ls -la "$stl_dest"/*.stl 2>/dev/null | tail -5
    else
        echo_warning "No STL files found to share"
    fi
}

# Upload files to Azure
upload_file() {
    local source_file="$1"
    local dest_name="${2:-$(basename "$source_file")}"
    
    if [ ! -f "$source_file" ]; then
        echo_error "Source file not found: $source_file"
        return 1
    fi
    
    local dest_path="$UPLOADS_DIR/$dest_name"
    
    echo_info "Uploading file to Azure: $source_file -> $dest_path"
    
    if cp "$source_file" "$dest_path"; then
        echo_success "File uploaded successfully"
        echo_info "Azure path: $dest_path"
    else
        echo_error "Upload failed"
        return 1
    fi
}

# Show Azure storage usage
show_usage() {
    echo_info "Azure File Share Usage:"
    df -h "$AZURE_MOUNT"
    echo ""
    
    echo_info "Directory sizes:"
    du -sh "$BACKUP_DIR" "$LOGS_DIR" "$UPLOADS_DIR" "$SHARED_DIR" 2>/dev/null || true
}

# Full backup (database + application + logs)
full_backup() {
    local env="${1:-production}"
    
    echo_info "Starting full backup for $env environment"
    
    backup_database "y3dhub_prod"
    backup_application "$env"
    archive_logs
    
    echo_success "Full backup completed!"
    show_usage
}

# Show help
show_help() {
    echo "Y3DHub Azure File Share Integration"
    echo "=================================="
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  check                    - Check Azure mount status"
    echo "  backup-db [db_name]      - Backup MySQL database (default: y3dhub_prod)"
    echo "  backup-app [env]         - Backup application files (production|staging)"
    echo "  archive-logs             - Archive application logs"
    echo "  share-stl                - Copy STL files to shared directory"
    echo "  upload [file] [name]     - Upload a file to Azure"
    echo "  usage                    - Show storage usage"
    echo "  full-backup [env]        - Full backup (db + app + logs)"
    echo "  help                     - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 backup-db y3dhub_prod"
    echo "  $0 backup-app production"
    echo "  $0 upload /path/to/file.txt important-file.txt"
    echo "  $0 full-backup production"
}

# Main execution
main() {
    case "${1:-help}" in
        "check")
            check_azure_mount
            ;;
        "backup-db")
            check_azure_mount
            backup_database "$2"
            ;;
        "backup-app")
            check_azure_mount
            backup_application "$2"
            ;;
        "archive-logs")
            check_azure_mount
            archive_logs
            ;;
        "share-stl")
            check_azure_mount
            share_stl_files
            ;;
        "upload")
            check_azure_mount
            upload_file "$2" "$3"
            ;;
        "usage")
            check_azure_mount
            show_usage
            ;;
        "full-backup")
            check_azure_mount
            full_backup "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
